import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { OnboardingProvider, useOnboarding } from '../contexts/OnboardingContext'
import { LoadingSpinner } from '../components/ui/LoadingSpinner'

// Onboarding step components
import WelcomeStep from '../components/onboarding/WelcomeStep'
import NameStep from '../components/onboarding/NameStep'
import AgeStep from '../components/onboarding/AgeStep'
import PersonaStep from '../components/onboarding/PersonaStep'
import ConsentStep from '../components/onboarding/ConsentStep'

function OnboardingFlow() {
  const { user } = useAuth()
  const { currentStep, isLoading } = useOnboarding()
  const navigate = useNavigate()

  // Check if user has already completed onboarding
  useEffect(() => {
    if (user?.profileData?.onboardingCompleted) {
      navigate('/dashboard', { replace: true })
    }
  }, [user, navigate])

  // Show loading spinner during submission
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-eclipse-950 via-aura-900 to-sunbeam-900">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-white font-medium">Setting up your profile...</p>
        </div>
      </div>
    )
  }

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return <WelcomeStep />
      case 1:
        return <NameStep />
      case 2:
        return <AgeStep />
      case 3:
        return <PersonaStep />
      case 4:
        return <ConsentStep />
      default:
        return <WelcomeStep />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-eclipse-950 via-aura-900 to-sunbeam-900">
      {/* Step content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen p-6">
        <div className="w-full max-w-lg">
          {renderStep()}
        </div>
      </div>
    </div>
  )
}

export default function OnboardingPage() {
  return (
    <OnboardingProvider>
      <OnboardingFlow />
    </OnboardingProvider>
  )
}
