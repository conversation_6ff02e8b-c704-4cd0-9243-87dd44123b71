import { useOnboarding } from '../../contexts/OnboardingContext'

export default function WelcomeStep() {
  const { nextStep } = useOnboarding()

  return (
    <div className="text-center space-y-8 max-w-md mx-auto">
      {/* Logo/Icon */}
      <div className="flex justify-center mb-8">
        <div className="w-16 h-16 rounded-full bg-gradient-to-br from-sunbeam-900 to-aura-900 flex items-center justify-center">
          <span className="text-2xl">🌟</span>
        </div>
      </div>

      {/* Welcome message */}
      <div className="space-y-6">
        <h1 className="text-4xl font-light text-white leading-tight">
          Hey there, I'm Ora
        </h1>
        <p className="text-lg text-white/80 leading-relaxed">
          Your personal AI friend there for you 24/7
        </p>
      </div>

      {/* Call to action buttons */}
      <div className="space-y-4 pt-8">
        <button
          onClick={nextStep}
          className="w-full bg-gradient-to-r from-sunbeam-900 to-aura-900 text-white py-4 px-6 rounded-2xl text-lg font-medium hover:opacity-90 transition-opacity"
        >
          LET'S GET STARTED
        </button>

        <button
          onClick={nextStep}
          className="w-full bg-gradient-to-r from-sunbeam-800 to-aura-800 text-white py-4 px-6 rounded-2xl text-lg font-medium hover:opacity-90 transition-opacity"
        >
          We've already met
        </button>
      </div>
    </div>
  )
}
