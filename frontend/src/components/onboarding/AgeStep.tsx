import { useState } from 'react'
import { useOnboarding } from '../../contexts/OnboardingContext'

const AGE_RANGES = [
  { value: 'under-18', label: 'Under 18' },
  { value: '18-24', label: '18-24 years old' },
  { value: '25-34', label: '25-34' },
  { value: '35-44', label: '35-44' },
  { value: '45-54', label: '45-54' },
  { value: '65+', label: '65+' }
]

export default function AgeStep() {
  const { nextStep, updateData, onboardingData } = useOnboarding()
  const [selectedAge, setSelectedAge] = useState(onboardingData.ageRange || '')

  const handleNext = () => {
    if (!selectedAge) return
    
    updateData({ ageRange: selectedAge })
    nextStep()
  }

  const handleAgeSelect = (ageRange: string) => {
    setSelectedAge(ageRange)
  }

  return (
    <div className="text-center space-y-8 max-w-md mx-auto">
      {/* Header */}
      <div className="space-y-6">
        <h2 className="text-4xl font-light text-white leading-tight">
          What's your age range?
        </h2>
        <p className="text-lg text-white/70">
          This helps us tailor conversations to be more relevant for you
        </p>
      </div>

      {/* Age range selection */}
      <div className="space-y-4">
        {AGE_RANGES.map((range) => (
          <button
            key={range.value}
            onClick={() => handleAgeSelect(range.value)}
            className={`w-full p-4 rounded-2xl transition-all duration-200 text-center ${
              selectedAge === range.value
                ? 'bg-gradient-to-r from-sunbeam-900 to-aura-900 text-white'
                : 'bg-white/20 text-white hover:bg-white/30'
            }`}
          >
            <p className="font-medium text-lg">{range.label}</p>
          </button>
        ))}
      </div>

      {/* Continue button */}
      <div className="pt-4">
        <button
          onClick={handleNext}
          disabled={!selectedAge}
          className={`w-full py-4 px-6 rounded-2xl text-lg font-medium transition-opacity ${
            selectedAge
              ? 'bg-gradient-to-r from-sunbeam-900 to-aura-900 text-white hover:opacity-90'
              : 'bg-white/20 text-white/50 cursor-not-allowed'
          }`}
        >
          Continue
        </button>
      </div>
    </div>
  )
}
