import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useOnboarding } from '../../contexts/OnboardingContext'
import { Check, ExternalLink } from 'lucide-react'
import { LoadingSpinner } from '../ui/LoadingSpinner'

export default function ConsentStep() {
  const { updateData, onboardingData, submitOnboarding, isLoading, error } = useOnboarding()
  const navigate = useNavigate()
  const [consents, setConsents] = useState({
    privacyPolicy: onboardingData.consent?.privacyPolicy || false,
    dataProcessing: onboardingData.consent?.dataProcessing || false,
    emotionAnalysis: onboardingData.consent?.emotionAnalysis || false
  })

  const handleConsentChange = (type: keyof typeof consents, value: boolean) => {
    const newConsents = { ...consents, [type]: value }
    setConsents(newConsents)
    updateData({ consent: newConsents })
  }

  const handleSubmit = async () => {
    try {
      await submitOnboarding()
      // Navigate to dashboard on success
      navigate('/dashboard', { replace: true })
    } catch (err) {
      // Error is handled by the context
      console.error('Onboarding submission failed:', err)
    }
  }

  const canProceed = consents.privacyPolicy // Only privacy policy is required

  return (
    <div className="text-center space-y-8 max-w-md mx-auto">
      {/* Header */}
      <div className="space-y-6">
        <h2 className="text-4xl font-light text-white leading-tight">
          Privacy & Consent
        </h2>
        <p className="text-lg text-white/70">
          Your privacy matters. Please review and accept our terms.
        </p>
      </div>

      {/* Privacy summary */}
      <div className="bg-white/10 rounded-2xl p-6 text-left">
        <h3 className="font-medium text-white mb-3">What we collect:</h3>
        <ul className="text-sm text-white/70 space-y-2">
          <li>• Your name and basic profile information</li>
          <li>• Conversation transcripts (to improve AI responses)</li>
          <li>• Emotion analysis data (to enhance empathy)</li>
          <li>• Usage analytics (to improve the product)</li>
        </ul>
      </div>

      {/* Consent checkboxes */}
      <div className="space-y-4 text-left">
        {/* Required: Privacy Policy */}
        <div className="flex items-start space-x-3 p-4 border border-white/20 rounded-2xl bg-white/10">
          <button
            onClick={() => handleConsentChange('privacyPolicy', !consents.privacyPolicy)}
            className={`w-6 h-6 rounded-lg border-2 flex items-center justify-center transition-all duration-200 ${
              consents.privacyPolicy
                ? 'bg-gradient-to-r from-sunbeam-900 to-aura-900 border-transparent'
                : 'border-white/30 hover:border-white/50'
            }`}
          >
            {consents.privacyPolicy && <Check className="w-4 h-4 text-white" />}
          </button>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <p className="font-medium text-white">Privacy Policy</p>
              <span className="text-xs bg-white/20 text-white px-2 py-1 rounded-full">Required</span>
            </div>
            <p className="text-sm text-white/70 mt-1">
              I agree to the{' '}
              <button className="text-white hover:underline inline-flex items-center">
                Privacy Policy
                <ExternalLink className="w-3 h-3 ml-1" />
              </button>
              {' '}and Terms of Service
            </p>
          </div>
        </div>

        {/* Optional: Data Processing */}
        <div className="flex items-start space-x-3 p-4 border border-white/20 rounded-2xl bg-white/5">
          <button
            onClick={() => handleConsentChange('dataProcessing', !consents.dataProcessing)}
            className={`w-6 h-6 rounded-lg border-2 flex items-center justify-center transition-all duration-200 ${
              consents.dataProcessing
                ? 'bg-gradient-to-r from-sunbeam-900 to-aura-900 border-transparent'
                : 'border-white/30 hover:border-white/50'
            }`}
          >
            {consents.dataProcessing && <Check className="w-4 h-4 text-white" />}
          </button>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <p className="font-medium text-white">Enhanced Processing</p>
              <span className="text-xs bg-white/20 text-white/70 px-2 py-1 rounded-full">Optional</span>
            </div>
            <p className="text-sm text-white/70 mt-1">
              Allow advanced data processing for personalized recommendations
            </p>
          </div>
        </div>

        {/* Optional: Emotion Analysis */}
        <div className="flex items-start space-x-3 p-4 border border-white/20 rounded-2xl bg-white/5">
          <button
            onClick={() => handleConsentChange('emotionAnalysis', !consents.emotionAnalysis)}
            className={`w-6 h-6 rounded-lg border-2 flex items-center justify-center transition-all duration-200 ${
              consents.emotionAnalysis
                ? 'bg-gradient-to-r from-sunbeam-900 to-aura-900 border-transparent'
                : 'border-white/30 hover:border-white/50'
            }`}
          >
            {consents.emotionAnalysis && <Check className="w-4 h-4 text-white" />}
          </button>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <p className="font-medium text-white">Emotion Insights</p>
              <span className="text-xs bg-white/20 text-white/70 px-2 py-1 rounded-full">Optional</span>
            </div>
            <p className="text-sm text-white/70 mt-1">
              Share emotion analysis data to help improve AI empathy
            </p>
          </div>
        </div>
      </div>

      {/* Error display */}
      {error && (
        <div className="bg-red-500/20 border border-red-400/30 rounded-2xl p-4">
          <p className="text-red-300 text-sm flex items-center space-x-2">
            <span>⚠️</span>
            <span>{error}</span>
          </p>
        </div>
      )}

      {/* Complete Setup button */}
      <div className="pt-4">
        <button
          onClick={handleSubmit}
          disabled={!canProceed || isLoading}
          className={`w-full py-4 px-6 rounded-2xl text-lg font-medium transition-opacity ${
            canProceed && !isLoading
              ? 'bg-gradient-to-r from-sunbeam-900 to-aura-900 text-white hover:opacity-90'
              : 'bg-white/20 text-white/50 cursor-not-allowed'
          }`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center space-x-2">
              <LoadingSpinner size="sm" />
              <span>Setting up...</span>
            </div>
          ) : (
            'Complete Setup'
          )}
        </button>
      </div>

      {/* Help text */}
      <div className="text-center">
        <p className="text-xs text-white/50">
          You can change these preferences anytime in your profile settings
        </p>
      </div>
    </div>
  )
}
